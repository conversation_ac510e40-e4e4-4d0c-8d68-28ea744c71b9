import os
from app.cores.tts.minimax import (
    MinimaxTTS,
    TTSRequest,
    VoiceSetting,
)
from app.utils.audio import AudioUtils
from app.utils.mse import MSEGateway

print('mse_token:', MSEGateway.get_access_token())
from dotenv import load_dotenv
load_dotenv(r'D:\project\ai-market\app\api_key.env')

api_key = os.getenv('minimax_api_key')
group_id = os.getenv('minimax_group_id')

print(api_key)

client = MinimaxTTS(api_key=api_key, group_id=group_id)
request = TTSRequest(
    model="speech-02-turbo",
    text="人工智能不是要替代人类",
    voice_setting=VoiceSetting(voice_id="giikins_Sha_Zhi")
)
response = client.text_to_speech(request)

# 将语音数据转换为MP3文件
audio_data = response.data['audio']
mp3_file_path = "output.mp3"

# 使用hex_to_mp3函数将二进制数据转换为MP3文件
url = AudioUtils.hex_to_mp3_and_upload(audio_data)
print(url)

print(f"语音文件已保存到: {mp3_file_path}")


# from app.cores.tts.minimax import MinimaxTTS
#
# # 初始化客户端
# client = MinimaxTTS(api_key=api_key, group_id=group_id)
#
# # 查询所有可用音色
# voices = client.get_voice(voice_type="all")
#
# # 查询特定类型音色
# system_voices = client.get_voice(voice_type="system")
# cloned_voices = client.get_voice(voice_type="voice_cloning")
# generated_voices = client.get_voice(voice_type="voice_generation")
# music_voices = client.get_voice(voice_type="music_generation")
#
# print(type(cloned_voices))
# print(cloned_voices)
# print(cloned_voices.model_dump(exclude_none=True))
